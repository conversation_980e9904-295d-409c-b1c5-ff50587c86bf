"use client";
import React, { useState, useEffect, useRef } from "react";
import { Poppins } from "next/font/google";
import {
    TranscriptionDetailItem,
    submitSuggestedEdit,
} from "@/src/api/knowledge-base.api";
import { useAudioContext } from "@/src/context/audio.context";
import SuggestionBoxModal from "./suggestionBoxModal";
import { message } from "antd";
import { getAuth } from "firebase/auth";
import { IoCopyOutline } from "react-icons/io5";
import { GrCopy } from "react-icons/gr";
import bvksConfig from "@/src/config/apps/bvks"


const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface TranscriptionContentProps {
    transcription: TranscriptionDetailItem;
    onPlayAudio?: () => void;
    showTimestamps?: boolean;
    isEditMode?: boolean;
    isAutoScrollEnabled?: boolean;
    autoStartFromHighlight?: boolean; // New prop to control auto-start from <em> tags
}

interface SentenceWithTimestamp {
    text: string;
    timestamp: string;
    timeInSeconds: number;
}

const TranscriptionContent = ({
    transcription,
    onPlayAudio,
    showTimestamps = false,
    isEditMode = false,
    isAutoScrollEnabled = false,
    autoStartFromHighlight = false,
}: TranscriptionContentProps) => {
    const {
        currentTime,
        seekTo,
        showPlayer,
        isPlaying,
        togglePlay,
        updateHighlightedSections,
    } = useAudioContext();
    const [hoveredSentenceIndex, setHoveredSentenceIndex] = useState<
        number | null
    >(null);
    const [activeSentenceIndex, setActiveSentenceIndex] = useState<
        number | null
    >(null);
    const [parsedParagraphs, setParsedParagraphs] = useState<
        SentenceWithTimestamp[][]
    >([]);
    const activeSentenceRef = useRef<HTMLSpanElement | null>(null);
    const highlightedSentenceRef = useRef<HTMLSpanElement | null>(null);
    const [editingSentence, setEditingSentence] = useState<{
        paragraphIndex: number;
        sentenceIndex: number;
        text: string;
        timestamp: string;
    } | null>(null);
    const [highlightedSections, setHighlightedSections] = useState<
        {
            timeInSeconds: number;
            text: string;
            timestamp: string;
        }[]
    >([]);
    const [hasAutoStarted, setHasAutoStarted] = useState(false);
    const [contextMenu, setContextMenu] = useState<{
        x: number;
        y: number;
        text: string;
    } | null>(null);
    const scrollableContentRef = useRef<HTMLDivElement | null>(null);

    // Function to convert timestamp (HH:MM:SS or MM:SS) to seconds
    const timestampToSeconds = (timestamp: string): number => {
        const parts = timestamp.split(":").map(Number);

        if (parts.length === 3) {
            // HH:MM:SS format
            const [hours, minutes, seconds] = parts;
            return hours * 3600 + minutes * 60 + seconds;
        } else if (parts.length === 2) {
            // MM:SS format
            const [minutes, seconds] = parts;
            return minutes * 60 + seconds;
        }

        // Fallback for invalid format
        return 0;
    };

    // Parse paragraphs and extract sentences with timestamps
    useEffect(() => {
        if (!transcription.transcription) return;

        const parsedData = transcription.transcription.map((paragraph) => {
            const sentences: SentenceWithTimestamp[] = [];
            // Updated regex to match both HH:MM:SS and MM:SS formats
            const timestampRegex = /\((\d+:\d+(?::\d+)?)\)/g;

            let match;

            // Find all timestamps in the paragraph
            while ((match = timestampRegex.exec(paragraph)) !== null) {
                const timestamp = match[1];
                const timeInSeconds = timestampToSeconds(timestamp);
                const startIndex = match.index + match[0].length;

                // Find the end of this sentence (next timestamp or end of paragraph)
                // Updated regex to match both HH:MM:SS and MM:SS formats
                const nextMatch = paragraph
                    .slice(startIndex)
                    .match(/\(\d+:\d+(?::\d+)?\)/);
                const endIndex =
                    nextMatch && nextMatch.index !== undefined
                        ? startIndex + nextMatch.index
                        : paragraph.length;

                // Extract the sentence text
                const text = paragraph.substring(startIndex, endIndex);

                sentences.push({
                    text,
                    timestamp,
                    timeInSeconds,
                });
            }

            return sentences;
        });

        setParsedParagraphs(parsedData);

        // Extract highlighted sections (sentences with <em> tags)
        const highlighted: {
            timeInSeconds: number;
            text: string;
            timestamp: string;
        }[] = [];

        parsedData.forEach((sentences) => {
            sentences.forEach((sentence) => {
                if (sentence.text.includes("<em>")) {
                    // Extract text inside <em> tags
                    const emMatches = sentence.text.match(/<em>(.*?)<\/em>/g);
                    if (emMatches) {
                        emMatches.forEach((match) => {
                            const cleanText = match.replace(/<\/?em>/g, "");
                            highlighted.push({
                                timeInSeconds: sentence.timeInSeconds,
                                text: cleanText,
                                timestamp: sentence.timestamp,
                            });
                        });
                    }
                }
            });
        });

        setHighlightedSections(highlighted);

        // Update highlighted sections in audio context
        if (updateHighlightedSections) {
            updateHighlightedSections(highlighted);
        }
    }, [transcription, updateHighlightedSections]);

    // Auto-start audio from first highlighted section when transcription opens
    useEffect(() => {
        if (
            autoStartFromHighlight &&
            !hasAutoStarted &&
            highlightedSections.length > 0 &&
            onPlayAudio
        ) {
            const firstHighlight = highlightedSections[0];

            // Start audio player if not already showing
            if (!showPlayer) {
                onPlayAudio();

                // Wait for audio to load, then seek to the highlighted timestamp
                setTimeout(() => {
                    if (seekTo) {
                        seekTo(firstHighlight.timeInSeconds);
                    }
                    // Start playing if not already playing
                    if (!isPlaying) {
                        togglePlay();
                    }

                    // Scroll to the highlighted sentence
                    if (highlightedSentenceRef.current) {
                        highlightedSentenceRef.current.scrollIntoView({
                            behavior: "smooth",
                            block: "center",
                        });
                    }
                }, 500); // Increased delay to ensure audio loads properly
            } else {
                // Audio player is already showing, just seek to the timestamp
                if (seekTo) {
                    seekTo(firstHighlight.timeInSeconds);
                }

                // Start playing if not already playing
                if (!isPlaying) {
                    togglePlay();
                }

                // Scroll to the highlighted sentence
                if (highlightedSentenceRef.current) {
                    highlightedSentenceRef.current.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                    });
                }
            }

            setHasAutoStarted(true);
        }
    }, [
        autoStartFromHighlight,
        hasAutoStarted,
        highlightedSections,
        onPlayAudio,
        showPlayer,
        seekTo,
        isPlaying,
        togglePlay,
    ]);

    // Update active sentence based on current playback time
    useEffect(() => {
        if (parsedParagraphs.length === 0) return;

        // Flatten all sentences from all paragraphs
        const allSentences = parsedParagraphs.flat();

        // Find the sentence that corresponds to the current time
        let activeIndex = -1;

        for (let i = 0; i < allSentences.length; i++) {
            if (allSentences[i].timeInSeconds <= currentTime) {
                activeIndex = i;
            } else {
                break;
            }
        }

        setActiveSentenceIndex(activeIndex >= 0 ? activeIndex : null);
    }, [currentTime, parsedParagraphs]);

    // Auto-scroll to active sentence when it changes
    useEffect(() => {
        if (
            isAutoScrollEnabled &&
            activeSentenceRef.current &&
            activeSentenceIndex !== null &&
            isPlaying
        ) {
            // Scroll the active sentence into view with smooth behavior
            activeSentenceRef.current.scrollIntoView({
                behavior: "smooth",
                block: "center",
            });
        }
    }, [activeSentenceIndex, isAutoScrollEnabled, isPlaying]);

    // Handle sentence click to seek to that timestamp or edit
    const handleSentenceClick = (
        timeInSeconds: number,
        paragraphIndex: number,
        sentenceIndex: number,
        text: string,
        timestamp: string
    ) => {
        // If in edit mode, start editing this sentence
        if (isEditMode) {
            setEditingSentence({
                paragraphIndex,
                sentenceIndex,
                text,
                timestamp,
            });
            return;
        }

        // Otherwise, handle audio playback as before
        // If audio player is not showing, start it first
        if (!showPlayer && onPlayAudio) {
            // Start the audio player
            onPlayAudio();

            // We need a small delay to allow the audio to load before seeking
            setTimeout(() => {
                if (seekTo) {
                    seekTo(timeInSeconds);
                }
                // Start playing if not already playing
                if (!isPlaying) {
                    togglePlay();
                }
            }, 300);
        } else {
            // Audio player is already showing, just seek to the timestamp
            if (seekTo) {
                seekTo(timeInSeconds);
            }

            // Start playing if not already playing
            if (!isPlaying) {
                togglePlay();
            }
        }
    };

    // Handle edit submission
    const handleSubmitEdit = async (newText: string, reason: string) => {
        if (!editingSentence) return;

        try {
            // Get current user's ID token if available
            const auth = getAuth();
            const user = auth.currentUser;
            let idToken = null;

            if (user) {
                idToken = await user.getIdToken();
            }

            // Prepare the payload
            const payload = {
                transcriptionId: transcription.id,
                timestamp: editingSentence.timestamp,
                originalText: editingSentence.text,
                suggestedText: newText,
                reason: reason,
            };

            // Submit the edit suggestion
            const response = await submitSuggestedEdit(payload, idToken);

            // Show success message
            message.success(
                "Your edit suggestion has been submitted successfully!"
            );

            // Close the edit form
            setEditingSentence(null);

            return response;
        } catch (error) {
            console.error("Error submitting edit suggestion:", error);
            message.error(
                "Failed to submit your edit suggestion. Please try again."
            );
            throw error;
        }
    };

    // Process text for newlines, <em>/<i> tag handling, &nbsp; entities, and auto-paragraph creation
    const processText = (text: string): React.ReactNode => {
        const PARAGRAPH_SIZE = 500;

        // First, handle &nbsp; entities by replacing them with regular spaces
        let processedText = text.replace(/&nbsp;/g, ' ');

        // Handle <i> tags by removing them (no highlighting needed)
        processedText = processedText.replace(/<\/?i>/g, '');

        // Check if text has newlines for natural paragraph breaks
        const hasNewlines = processedText.includes('\n');

        let parts: string[];

        if (hasNewlines) {
            // Handle newlines normally
            parts = processedText.split(/\n+/);
        } else {
            // No newlines found, create artificial paragraphs based on PARAGRAPH_SIZE
            parts = [];
            let currentPart = '';
            const words = processedText.split(' ');

            for (const word of words) {
                if (currentPart.length + word.length + 1 > PARAGRAPH_SIZE && currentPart.length > 0) {
                    // Current part would exceed PARAGRAPH_SIZE, start a new part
                    parts.push(currentPart.trim());
                    currentPart = word;
                } else {
                    // Add word to current part
                    currentPart += (currentPart.length > 0 ? ' ' : '') + word;
                }
            }

            // Add the last part if it has content
            if (currentPart.trim().length > 0) {
                parts.push(currentPart.trim());
            }
        }

        return parts.map((part, index) => {
            let content = part;

            // Check if text contains <em> tags and highlight them (keep existing highlighting)
            if (part.includes("<em>")) {
                try {
                    // Replace <em> tags with highlighted spans
                    content = part.replace(
                        /<em>(.*?)<\/em>/g,
                        '<span class="bg-yellow-200 italic font-medium text-primary">$1</span>'
                    );
                } catch (error) {
                    console.error("Error highlighting <em> tags:", error);
                }
            }

            // Add appropriate spacing based on whether we have natural newlines or artificial paragraphs
            const isDoubleNewline = hasNewlines && index > 0 && text.includes("\n\n");
            const isArtificialParagraph = !hasNewlines && index > 0;

            return (
                <React.Fragment key={index}>
                    {index > 0 &&
                        (isDoubleNewline ? (
                            <div className="my-2"></div> // More spacing for paragraph breaks
                        ) : isArtificialParagraph ? (
                            <div className="my-3"></div> // Spacing for artificial paragraphs
                        ) : (
                            <>
                                <br />
                                <br />{" "}
                            </>
                        ))}
                    {part.includes("<em>") ? (
                        <span dangerouslySetInnerHTML={{ __html: content }} />
                    ) : (
                        content
                    )}
                </React.Fragment>
            );
        });
    };

    // Calculate global sentence index
    const getGlobalSentenceIndex = (
        paragraphIndex: number,
        sentenceIndex: number
    ): number => {
        let globalIndex = sentenceIndex;
        for (let i = 0; i < paragraphIndex; i++) {
            globalIndex += parsedParagraphs[i]?.length || 0;
        }
        return globalIndex;
    };

    // Hide context menu on click elsewhere
    useEffect(() => {
        const handleClick = () => setContextMenu(null);
        window.addEventListener("click", handleClick);
        return () => window.removeEventListener("click", handleClick);
    }, []);

    // Helper to get selected text inside scrollable area
    const getSelectedText = () => {
        const selection = window.getSelection();
        if (!selection || selection.isCollapsed) return "";
        // Only allow selection inside the scrollable content
        if (
            scrollableContentRef.current &&
            selection.anchorNode &&
            scrollableContentRef.current.contains(selection.anchorNode)
        ) {
            return selection.toString();
        }
        return "";
    };

    // Handle right-click (context menu) in scrollable area
    const handleContextMenu = (e: React.MouseEvent<HTMLDivElement>) => {
        const selectedText = getSelectedText();
        if (selectedText) {
            e.preventDefault();
            setContextMenu({ x: e.clientX, y: e.clientY, text: selectedText });
        }
    };

    // Copy handlers
    const handleCopy = async () => {
        if (contextMenu?.text) {
            await navigator.clipboard.writeText(contextMenu.text);
            setContextMenu(null);
        }
    };

    const handleCopyWithReference = async () => {
        if (contextMenu?.text) {
            const reference = `${bvksConfig.appName} Knowledge Base\n\n"${
                contextMenu.text
            }"\n\n------------------\n\nTitle: ${
                transcription.title || ""
            }\n\nRecorded On: ${
                transcription.dateOfRecording || ""
            }\n\nPlace: ${transcription.place || ""}`;
            await navigator.clipboard.writeText(reference);
            setContextMenu(null);
        }
    };

    return (
        <div className={`${poppins.className} pb-32 relative`}>
            {/* Fixed notification banners that don't scroll */}
            <div className="sticky top-0 z-10 bg-white pb-4 space-y-4">
                {isEditMode && (
                    <div
                        className={`bg-green-100 p-3 rounded-md border border-green-300`}
                    >
                        <p className="text-green-800 text-sm">
                            <span className="font-semibold">
                                Suggest Edit mode is active.
                            </span>{" "}
                            Click on any sentence to suggest an edit for that
                            specific timestamp.
                        </p>
                    </div>
                )}

                {isAutoScrollEnabled && (
                    <div className="bg-green-100 p-3 rounded-md border border-green-300">
                        <p className="text-green-800 text-sm">
                            <span className="font-semibold">
                                Auto Scroll is active.
                            </span>{" "}
                            The transcript will automatically scroll to follow
                            the audio playback.
                        </p>
                    </div>
                )}
            </div>

            {/* Scrollable content area */}
            <style>{`
                .bvks-transcription-scrollable ::selection {
                    background: #e5e7eb; /* Tailwind gray-200 */
                    color: #111827; /* Tailwind gray-900 */
                }
            `}</style>
            <div
                className="bg-white rounded-lg bvks-transcription-scrollable"
                ref={scrollableContentRef}
                onContextMenu={handleContextMenu}
                style={{ position: "relative" }}
            >
                {/* Custom context menu */}
                {contextMenu && (
                    <ul
                        style={{
                            position: "fixed",
                            top: contextMenu.y,
                            left: contextMenu.x,
                            zIndex: 1000,
                            background: "#fff",
                            borderRadius: 6,
                            boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                            padding: 0,
                            margin: 0,
                            listStyle: "none",
                            minWidth: 180,
                        }}
                        className="border-primary"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <li
                            style={{
                                padding: "10px 16px",
                                cursor: "pointer",
                                display: "flex",
                                alignItems: "center"
                            }}
                            onClick={handleCopy}
                            className="flex items-center cursor-pointer px-4 py-2 gap-2 hover:bg-primary-hover hover:rounded-[6px]"
                        >
                            <IoCopyOutline className="text-primary " />
                            Copy
                        </li>
                        <li
                            style={{
                                padding: "10px 16px",
                                cursor: "pointer",
                                display: "flex",
                                alignItems: "center"
                            }}
                            className="flex items-center cursor-pointer px-4 py-2 gap-2 hover:bg-primary-hover hover:rounded-[6px]"
                            onClick={handleCopyWithReference}
                        >
                            <GrCopy className="text-primary" />
                            Copy with reference
                        </li>
                    </ul>
                )}
                <div className="space-y-4">
                    {parsedParagraphs.length > 0 ? (
                        parsedParagraphs.map((sentences, paragraphIndex) => (
                            <div key={paragraphIndex} className="mb-6">
                                <div className="text-gray-800 text-base leading-relaxed text-justify">
                                    {sentences.map(
                                        (sentence, sentenceIndex) => {
                                            const globalIndex =
                                                getGlobalSentenceIndex(
                                                    paragraphIndex,
                                                    sentenceIndex
                                                );
                                            const isHovered =
                                                hoveredSentenceIndex ===
                                                globalIndex;
                                            const isActive =
                                                activeSentenceIndex ===
                                                globalIndex;
                                            const isEditing =
                                                isEditMode &&
                                                editingSentence?.paragraphIndex ===
                                                    paragraphIndex &&
                                                editingSentence?.sentenceIndex ===
                                                    sentenceIndex;
                                            const hasEmTag =
                                                sentence.text.includes("<em>");
                                            const isFirstHighlighted =
                                                hasEmTag &&
                                                highlightedSections.length >
                                                    0 &&
                                                sentence.timeInSeconds ===
                                                    highlightedSections[0]
                                                        .timeInSeconds;

                                            return (
                                                <span key={sentenceIndex}>
                                                    {showTimestamps && (
                                                        <span
                                                            className="text-base text-[#3fa1d1] cursor-pointer"
                                                            onClick={() =>
                                                                handleSentenceClick(
                                                                    sentence.timeInSeconds,
                                                                    paragraphIndex,
                                                                    sentenceIndex,
                                                                    sentence.text,
                                                                    sentence.timestamp
                                                                )
                                                            }
                                                        >
                                                            (
                                                            {sentence.timestamp}
                                                            )
                                                        </span>
                                                    )}
                                                    <span
                                                        ref={
                                                            isActive
                                                                ? activeSentenceRef
                                                                : isFirstHighlighted
                                                                ? highlightedSentenceRef
                                                                : null
                                                        }
                                                        className={`cursor-pointer transition-colors ${
                                                            isHovered
                                                                ? "bg-[#e6f7ff]"
                                                                : ""
                                                        } ${
                                                            isActive
                                                                ? "bg-primary bg-opacity-70 font-medium"
                                                                : ""
                                                        } ${
                                                            isEditMode
                                                                ? "hover:bg-green-100"
                                                                : ""
                                                        } ${
                                                            isEditing
                                                                ? "bg-green-200"
                                                                : ""
                                                        }`}
                                                        onMouseEnter={() =>
                                                            setHoveredSentenceIndex(
                                                                globalIndex
                                                            )
                                                        }
                                                        onMouseLeave={() =>
                                                            setHoveredSentenceIndex(
                                                                null
                                                            )
                                                        }
                                                        onClick={() =>
                                                            handleSentenceClick(
                                                                sentence.timeInSeconds,
                                                                paragraphIndex,
                                                                sentenceIndex,
                                                                sentence.text,
                                                                sentence.timestamp
                                                            )
                                                        }
                                                    >
                                                        {processText(
                                                            sentence.text
                                                        )}
                                                    </span>
                                                </span>
                                            );
                                        }
                                    )}
                                </div>
                            </div>
                        ))
                    ) : transcription.transcription &&
                      transcription.transcription.length > 0 ? (
                        <div className="text-center py-4">
                            <p className="text-gray-500">
                                Processing transcription...
                            </p>
                        </div>
                    ) : (
                        <p className="text-gray-500 italic">
                            No transcription available for this content.
                        </p>
                    )}
                </div>
            </div>

            {/* Suggestion Box Modal */}
            {editingSentence && (
                <SuggestionBoxModal
                    isModalOpen={!!editingSentence}
                    setIsModalOpen={(isOpen) => {
                        if (!isOpen) setEditingSentence(null);
                    }}
                    timestamp={editingSentence.timestamp}
                    originalText={editingSentence.text}
                    transcriptionId={transcription.id}
                    onSubmit={handleSubmitEdit}
                />
            )}
        </div>
    );
};

export default TranscriptionContent;
